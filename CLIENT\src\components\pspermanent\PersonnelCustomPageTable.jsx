import React, { useState, useEffect, useCallback, useMemo } from "react";
import CustomMenu from "./PersonnelCustomMenu";
import CustomTable from "./PersonnelCustomTable";
import ExportExcelButton from "./ExportExcelButton";
import PropTypes from "prop-types";
import {
  Button,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Chip,
  FormControlLabel,
  Switch,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  Card,
  CardContent,
  Grid,
  Paper,
  Checkbox,
  FormControl,
  InputLabel,
  Select,
} from "@mui/material";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import RefreshIcon from '@mui/icons-material/Refresh';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import FilterListIcon from '@mui/icons-material/FilterList';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import GetAppIcon from '@mui/icons-material/GetApp';
import DeleteSweepIcon from '@mui/icons-material/DeleteSweep';
import EditIcon from '@mui/icons-material/Edit';
import PrintIcon from '@mui/icons-material/Print';
import { useQueryClient } from "@tanstack/react-query";

const PSPCustomPageTable = ({
  dataListName,
  schema,
  title = "",
  description = "",
  searchable = true,
  hasEdit = true,
  hasDelete = true,
  hasAdd = true,
  customAddElement = null,
  customEditElement = null,
  additionalMenuOptions = [],
  ROWS_PER_PAGE = 20,
}) => {
  const [rows, setRows] = useState([]);
  const [filteredRows, setFilteredRows] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fiscalYear, setFiscalYear] = useState("");
  const [budgetType, setBudgetType] = useState("");
  const [PERA, setPERA] = useState(0);
  const [RATA, setRATA] = useState([]);
  const [compensation, setCompensation] = useState({});
  const { currentUser } = useUser();
  const queryClient = useQueryClient();

  // Enhanced state management
  const [selectedRows, setSelectedRows] = useState([]);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(null);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [anchorEl, setAnchorEl] = useState(null);
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);
  const [bulkActionAnchor, setBulkActionAnchor] = useState(null);
  const [exportMenuAnchor, setExportMenuAnchor] = useState(null);
  const [viewDetailsDialog, setViewDetailsDialog] = useState({ open: false, data: null });
  const [bulkDeleteDialog, setBulkDeleteDialog] = useState(false);
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    totalSalary: 0,
  });

  const pageTitle = useMemo(() => title || dataListName.charAt(0).toUpperCase() + dataListName.slice(1), [title, dataListName]);
  const pageDescription = useMemo(() => description || `Manage ${dataListName}`, [description, dataListName]);
  const apiPath = useMemo(() => `/${dataListName}`, [dataListName]);

  // Initialize column visibility
  useEffect(() => {
    const initialVisibility = {};
    Object.keys(schema).forEach(key => {
      initialVisibility[key] = schema[key].show !== false;
    });
    setColumnVisibility(initialVisibility);
  }, [schema]);

  // Calculate statistics
  useEffect(() => {
    if (rows.length > 0) {
      const stats = {
        total: rows.length,
        active: rows.filter(row => row.employeeStatus === 'Active').length,
        inactive: rows.filter(row => row.employeeStatus === 'Inactive').length,
        totalSalary: rows.reduce((sum, row) => sum + (row.Total || 0), 0),
      };
      setStatistics(stats);
    }
  }, [rows]);

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
      setRefreshInterval(interval);
      return () => clearInterval(interval);
    } else if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }
  }, [autoRefresh, fetchData]);

  // Enhanced menu handlers
  const handleMenuClick = (event) => setAnchorEl(event.currentTarget);
  const handleMenuClose = () => setAnchorEl(null);
  const handleFilterMenuClick = (event) => setFilterMenuAnchor(event.currentTarget);
  const handleFilterMenuClose = () => setFilterMenuAnchor(null);
  const handleBulkActionClick = (event) => setBulkActionAnchor(event.currentTarget);
  const handleBulkActionClose = () => setBulkActionAnchor(null);
  const handleExportMenuClick = (event) => setExportMenuAnchor(event.currentTarget);
  const handleExportMenuClose = () => setExportMenuAnchor(null);

  // Column visibility toggle
  const toggleColumnVisibility = (columnKey) => {
    setColumnVisibility(prev => ({
      ...prev,
      [columnKey]: !prev[columnKey]
    }));
  };

  // Manual refresh
  const handleManualRefresh = useCallback(async () => {
    setLoading(true);
    try {
      await fetchData();
      await fetchActiveSettings();
      await fetchRATA();
      toast.success("Data refreshed successfully!");
    } catch (error) {
      toast.error("Failed to refresh data");
    } finally {
      setLoading(false);
    }
  }, [fetchData, fetchActiveSettings, fetchRATA]);

  // View details handler
  const handleViewDetails = (row) => {
    setViewDetailsDialog({ open: true, data: row });
  };

  // Bulk delete handler
  const handleBulkDelete = async () => {
    if (selectedRows.length === 0) {
      toast.warning("Please select rows to delete");
      return;
    }
    setBulkDeleteDialog(true);
  };

  const confirmBulkDelete = async () => {
    setLoading(true);
    try {
      await Promise.all(
        selectedRows.map(id => api.delete(`${apiPath}/${id}`))
      );
      toast.success(`${selectedRows.length} records deleted successfully`);
      setSelectedRows([]);
      await fetchData();
      queryClient.invalidateQueries(dataListName);
    } catch (error) {
      toast.error("Failed to delete selected records");
    } finally {
      setLoading(false);
      setBulkDeleteDialog(false);
    }
  };

  const fetchActiveSettings = useCallback(async () => {
    try {
      const response = await api.get("/settings/active");
      if (response.data) {
        setFiscalYear(response.data.fiscalYear || "");
        setBudgetType(response.data.budgetType || "");
        setPERA(response.data.PERA || 0);
        setCompensation({
          uniformALLOWANCE: response.data.uniformAllowance,
          productivityIncentive: response.data.productivityIncentive,
          medicalAllowance: response.data.medicalAllowance,
          meal: response.data.meal,
          cashGift: response.data.cashGift,
          pagibigPremium: response.data.pagibigPremium,
          gsisPremium: response.data.gsisPremium,
          employeeCompensation: response.data.employeeCompensation,
        });
      } else {
        toast.error("Active settings not found.");
      }
    } catch (error) {
      toast.error("Error fetching active settings.");
      console.error("Error fetching active settings:", error);
    }
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const response = await api.get(`${apiPath}?statusOfAppointment=${encodeURIComponent('PERMANENT|Permanent')}`);
      if (response.data && Array.isArray(response.data.personnelServices)) {
        const permanentPersonnel = response.data.personnelServices;
        setRows(permanentPersonnel);
        setFilteredRows(permanentPersonnel);
      } else {
        toast.error("Fetched data is not in the expected format.");
        console.error("Fetched data is not an array:", response.data);
      }
    } catch (error) {
      toast.error("Error fetching personnel data.");
      console.error("Error fetching data:", error);
    }
  }, [apiPath]);

  const fetchRATA = useCallback(async () => {
    try {
      const response = await api.get("/ratas");
      if (response.data && Array.isArray(response.data.ratas)) {
        setRATA(response.data.ratas);
      } else {
        toast.error("RATA data format incorrect.");
        console.error("RATA response format incorrect:", response.data);
      }
    } catch (error) {
      toast.error("Error fetching RATA data.");
      console.error("Error fetching RATA data:", error);
    }
  }, []);

  useEffect(() => {
    fetchData();
    fetchActiveSettings();
    fetchRATA();
  }, [fetchData, fetchActiveSettings, fetchRATA]);

  useEffect(() => {
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, [fetchData]);

  useEffect(() => {
    setFilteredRows(rows);
  }, [rows]);

  const handleAddAllPersonnel = useCallback(async (statusOfAppointment) => {
    if (!fiscalYear) {
      toast.error("Active fiscal year not set. Please try again later.");
      return;
    }

    setLoading(true);

    try {
      const processBy = `${currentUser.FirstName} ${currentUser.LastName}`;

      const response = await api.post(`/personnelServices/bulk-add`, {
        processBy,
        statusOfAppointment,
        fiscalYear,
        budgetType,
        PERA,
        RATA: RATA.map(item => ({ grade: item.SG, amount: item.RATA })),
        compensation,
      });

      if (Array.isArray(response.data)) {
        toast.success("Personnel successfully added!");
        await fetchData();
        queryClient.invalidateQueries(dataListName);
      } else {
        toast.info(response.data.message || "Bulk add failed");
        console.error("Bulk add response is not an array:", response.data);
      }

    } catch (error) {
      if (error.response?.status === 403) {
        toast.error(error.response.data.message || "Submission is locked.");
      } else {
        toast.error("Error bulk adding personnel.");
      }
      console.error("Error bulk adding personnel:", error);
    } finally {
      setLoading(false);
      setOpenDialog(false);
    }
  }, [fiscalYear, budgetType, PERA, RATA, compensation, currentUser, fetchData, queryClient, dataListName]);

  const handleOpenDialog = () => setOpenDialog(true);
  const handleCloseDialog = () => setOpenDialog(false);

  // Enhanced additional menu options
  const enhancedMenuOptions = useMemo(() => {
    const options = [...additionalMenuOptions];

    // Add view details option
    options.push((props) => (
      <MenuItem
        key="view-details"
        onClick={() => {
          handleViewDetails(props.row);
          props.parentClose();
        }}
        sx={{ display: "flex", gap: 1 }}
      >
        <VisibilityIcon fontSize="small" />
        View Details
      </MenuItem>
    ));

    // Add duplicate record option
    options.push((props) => (
      <MenuItem
        key="duplicate-record"
        onClick={() => {
          // Create a copy of the record without _id
          const duplicateData = { ...props.row };
          delete duplicateData._id;
          delete duplicateData.createdAt;
          delete duplicateData.updatedAt;
          // You can implement duplicate functionality here
          toast.info("Duplicate functionality to be implemented");
          props.parentClose();
        }}
        sx={{ display: "flex", gap: 1 }}
      >
        <EditIcon fontSize="small" />
        Duplicate Record
      </MenuItem>
    ));

    // Add print option
    options.push((props) => (
      <MenuItem
        key="print-record"
        onClick={() => {
          // Create a print-friendly view of the record
          const printWindow = window.open('', '_blank');
          const printContent = `
            <html>
              <head>
                <title>Personnel Record - ${props.row.employeeFullName}</title>
                <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .header { text-align: center; margin-bottom: 30px; }
                  .field { margin-bottom: 10px; }
                  .label { font-weight: bold; display: inline-block; width: 200px; }
                  .value { display: inline-block; }
                </style>
              </head>
              <body>
                <div class="header">
                  <h2>Personnel Record</h2>
                  <h3>${props.row.employeeFullName}</h3>
                </div>
                ${Object.keys(schema).map(key =>
                  schema[key].label && key !== 'action' ?
                    `<div class="field">
                      <span class="label">${schema[key].label}:</span>
                      <span class="value">${props.row[key] || 'N/A'}</span>
                    </div>` : ''
                ).join('')}
              </body>
            </html>
          `;
          printWindow.document.write(printContent);
          printWindow.document.close();
          printWindow.print();
          props.parentClose();
        }}
        sx={{ display: "flex", gap: 1 }}
      >
        <PrintIcon fontSize="small" />
        Print Record
      </MenuItem>
    ));

    return options;
  }, [additionalMenuOptions, schema]);

  return (
    <>
      {/* Statistics Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Personnel
              </Typography>
              <Typography variant="h4">
                {statistics.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active
              </Typography>
              <Typography variant="h4" color="success.main">
                {statistics.active}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Inactive
              </Typography>
              <Typography variant="h4" color="error.main">
                {statistics.inactive}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Salary
              </Typography>
              <Typography variant="h4" color="primary.main">
                ₱{statistics.totalSalary.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Enhanced Action Bar */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" flexWrap="wrap" gap={2}>
          <Box display="flex" alignItems="center" gap={2}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenDialog}
              sx={{
                background: "#009688",
                color: "#fff",
                "&:hover": {
                  background: "#00796B",
                  color: "#fff",
                  textDecoration: "underline rgb(255, 255, 255)"
                },
              }}
              startIcon={<PersonAddIcon />}
            >
              Add Personnel
            </Button>

            <Tooltip title="Refresh Data">
              <IconButton
                onClick={handleManualRefresh}
                disabled={loading}
                color="primary"
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <FormControlLabel
              control={
                <Switch
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  color="primary"
                />
              }
              label="Auto Refresh"
            />
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            {selectedRows.length > 0 && (
              <Chip
                label={`${selectedRows.length} selected`}
                color="primary"
                variant="outlined"
              />
            )}

            <Tooltip title="Column Visibility">
              <IconButton onClick={handleFilterMenuClick}>
                <VisibilityIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Export Options">
              <IconButton onClick={handleExportMenuClick}>
                <GetAppIcon />
              </IconButton>
            </Tooltip>

            {selectedRows.length > 0 && (
              <Tooltip title="Bulk Actions">
                <IconButton onClick={handleBulkActionClick}>
                  <MoreVertIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
      </Paper>

      {/* Column Visibility Menu */}
      <Menu
        anchorEl={filterMenuAnchor}
        open={Boolean(filterMenuAnchor)}
        onClose={handleFilterMenuClose}
      >
        <MenuItem disabled>
          <Typography variant="subtitle2">Toggle Columns</Typography>
        </MenuItem>
        <Divider />
        {Object.keys(schema).map((key) => (
          schema[key].label && (
            <MenuItem key={key}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={columnVisibility[key] || false}
                    onChange={() => toggleColumnVisibility(key)}
                  />
                }
                label={schema[key].label}
              />
            </MenuItem>
          )
        ))}
      </Menu>

      {/* Export Menu */}
      <Menu
        anchorEl={exportMenuAnchor}
        open={Boolean(exportMenuAnchor)}
        onClose={handleExportMenuClose}
      >
        <Box sx={{ p: 1 }}>
          <ExportExcelButton
            columns={Object.keys(schema)
              .filter((key) => (columnVisibility[key] !== false && (schema[key].show === true || key === "action")))
              .map((key) => ({
                field: key,
                label: schema[key].label,
                type: schema[key].type,
              }))
              .filter(col => col.field !== 'action')
            }
            data={filteredRows}
            filename="Permanent_Personnel"
          />
        </Box>
        <MenuItem onClick={() => {
          window.print();
          handleExportMenuClose();
        }}>
          <ListItemIcon>
            <PrintIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Print Table</ListItemText>
        </MenuItem>
      </Menu>

      {/* Bulk Actions Menu */}
      <Menu
        anchorEl={bulkActionAnchor}
        open={Boolean(bulkActionAnchor)}
        onClose={handleBulkActionClose}
      >
        <MenuItem onClick={() => {
          handleBulkDelete();
          handleBulkActionClose();
        }}>
          <ListItemIcon>
            <DeleteSweepIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Delete Selected</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          // Bulk edit functionality
          handleBulkActionClose();
        }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Bulk Edit</ListItemText>
        </MenuItem>
      </Menu>

      {/* Bulk Add Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>Confirm Bulk Add</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to add all personnel with a status of appointment as PERMANENT?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            Cancel
          </Button>
          <Button
            onClick={() => handleAddAllPersonnel("PERMANENT")}
            color="primary"
            autoFocus
          >
            {loading ? <CircularProgress size={24} /> : "Yes"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bulk Delete Confirmation Dialog */}
      <Dialog open={bulkDeleteDialog} onClose={() => setBulkDeleteDialog(false)}>
        <DialogTitle>Confirm Bulk Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete {selectedRows.length} selected personnel records? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkDeleteDialog(false)} color="primary">
            Cancel
          </Button>
          <Button
            onClick={confirmBulkDelete}
            color="error"
            autoFocus
          >
            {loading ? <CircularProgress size={24} /> : "Delete"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Details Dialog */}
      <Dialog
        open={viewDetailsDialog.open}
        onClose={() => setViewDetailsDialog({ open: false, data: null })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Personnel Details</DialogTitle>
        <DialogContent>
          {viewDetailsDialog.data && (
            <Grid container spacing={2}>
              {Object.keys(schema).map((key) => (
                schema[key].label && key !== 'action' && (
                  <Grid item xs={12} sm={6} key={key}>
                    <Typography variant="subtitle2" color="textSecondary">
                      {schema[key].label}
                    </Typography>
                    <Typography variant="body1">
                      {schema[key].type === 'number'
                        ? (viewDetailsDialog.data[key] || 0).toLocaleString()
                        : viewDetailsDialog.data[key] || 'N/A'
                      }
                    </Typography>
                  </Grid>
                )
              ))}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDetailsDialog({ open: false, data: null })}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />

      <CustomTable
        ROWS_PER_PAGE={ROWS_PER_PAGE}
        dataListName={dataListName}
        apiPath={apiPath}
        rows={filteredRows}
        selectedRows={selectedRows}
        onRowSelectionChange={setSelectedRows}
        columns={useMemo(() => Object.keys(schema)
          .filter((key) => (columnVisibility[key] !== false && (schema[key].show === true || key === "action")))
          .map((key) => {
            const fieldSchema = schema[key];
            const column = {
              field: key,
              label: fieldSchema.label,
              type: fieldSchema.type,
              searchable: fieldSchema.searchable || false,
              textAlign: fieldSchema.alignRight ? "right" : "left",
            };

            if (fieldSchema.type === "action") {
              column.render = (row) => (
                <CustomMenu
                  additionalMenuOptions={enhancedMenuOptions}
                  customEditElement={customEditElement}
                  hasEdit={hasEdit}
                  hasDelete={hasDelete}
                  row={row}
                  schema={schema}
                  endpoint={apiPath}
                  dataListName={dataListName}
                  disableEdit={row.status === "Submitted" || row.status === "Approved"}
                  disableDelete={row.status === "Submitted" || row.status === "Approved"}
                />
              );
            }

            if (fieldSchema.customRender) {
              column.render = (row) => fieldSchema.customRender(row);
            }

            return column;
          }), [schema, columnVisibility, enhancedMenuOptions, customEditElement, hasEdit, hasDelete, apiPath, dataListName])}
      />
    </>
  );
};

PSPCustomPageTable.propTypes = {
  dataListName: PropTypes.string.isRequired,
  schema: PropTypes.objectOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      show: PropTypes.bool,
      searchable: PropTypes.bool,
      customRender: PropTypes.func,
      default: PropTypes.any,
      alignRight: PropTypes.bool,
    })
  ).isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  searchable: PropTypes.bool,
  hasEdit: PropTypes.bool,
  hasDelete: PropTypes.bool,
  hasAdd: PropTypes.bool,
  customAddElement: PropTypes.element,
  customEditElement: PropTypes.element,
  additionalMenuOptions: PropTypes.array,
  ROWS_PER_PAGE: PropTypes.number,
};

export default PSPCustomPageTable;